import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import PointsPrompt from './PointsPrompt';
import { getFirstName, getLastName, getPointsBalance } from 'store/user/userSelectors';
import { Decimal } from 'decimal.js';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { useLogout } from 'lib/oauth';

jest.mock('store/user/userSelectors', () => ({
  getFirstName: jest.fn(),
  getLastName: jest.fn(),
  getPointsBalance: jest.fn(),
}));

jest.mock('lib/oauth', () => ({
  useLogout: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, ...props }) => (
    <div data-testid="roo-ui-box" {...props}>
      {children}
    </div>
  ),
  Text: ({ children, ...props }) => (
    <span data-testid="roo-ui-text" {...props}>
      {children}
    </span>
  ),
  NakedButton: ({ children, ...props }) => (
    <button data-testid="roo-ui-naked-button" {...props}>
      {children}
    </button>
  ),
  Flex: ({ children, ...props }) => (
    <div data-testid="roo-ui-flex" {...props}>
      {children}
    </div>
  ),
  Image: (props, alt) => <img data-testid="roo-ui-mage" alt={alt} {...props} />,
  Icon: (props) => <span data-testid="roo-ui-con" {...props}></span>,
}));

const mockLogout = jest.fn();
const mockStore = configureStore([]);

const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <PointsPrompt />
    </Provider>,
  );
};

describe('PointsPrompt component', () => {
  beforeEach(() => {
    getFirstName.mockReturnValue('firstName');
    getLastName.mockReturnValue('lastName');
    getPointsBalance.mockReturnValue(new Decimal(9000));
    useLogout.mockReturnValue({ logout: mockLogout });
  });
  it('renders the welcome text and points balance correctly', () => {
    renderComponent();

    expect(screen.getByText(/Welcome back firstName lastName/i)).toBeInTheDocument();
    expect(screen.getByText(/Available Qantas Points:/i)).toBeInTheDocument();
    expect(screen.getByText(/9,000 PTS/i)).toBeInTheDocument();
  });

  it('renders the "Not You? Log Out" button', () => {
    renderComponent();
    expect(screen.getByTestId('roo-ui-naked-button')).toBeInTheDocument();
  });

  it('does not display a specific name if selectors return empty', () => {
    getFirstName.mockReturnValue('');
    getLastName.mockReturnValue('');
    renderComponent();
    expect(screen.queryByText(/Hello firstName lastName/i)).not.toBeInTheDocument();
  });

  it('allows the "Not You? Log Out" button to be clicked', async () => {
    renderComponent();
    const logoutButton = screen.getByTestId('roo-ui-naked-button');
    expect(logoutButton).toBeEnabled();
    await userEvent.click(logoutButton);
    expect(mockLogout).toHaveBeenCalled();
  });
});
