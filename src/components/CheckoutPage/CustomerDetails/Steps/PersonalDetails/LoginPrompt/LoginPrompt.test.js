import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import LoginPrompt from './LoginPrompt';

import { useLoginUrl } from 'lib/qffAuth';
import { useDataLayer } from 'hooks/useDataLayer';
import useCtaClickEvent from 'hooks/useCtaClickEvent';
import userEvent from '@testing-library/user-event';

const mockCtaClickEvent = jest.fn();
const emitInteractionEvent = jest.fn();

jest.mock('lib/qffAuth');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/useCtaClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    ctaClickEvent: mockCtaClickEvent,
  })),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, 'data-testid': dataTestId, ...props }) => (
    <div data-testid={dataTestId} {...props}>
      {children}
    </div>
  ),
  Text: ({ children, 'data-testid': dataTestId, ...props }) => (
    <span data-testid={dataTestId} {...props}>
      {children}
    </span>
  ),
  Link: ({ children, ...props }) => (
    <a data-testid="roo-ui-link" {...props}>
      {children}
    </a>
  ),
  Button: ({ children, 'data-testid': dataTestId, ...props }) => (
    <button data-testid={dataTestId} {...props}>
      {children}
    </button>
  ),
  Flex: ({ children, ...props }) => (
    <div data-testid="roo-ui-flex" style={{ display: 'flex' }} {...props}>
      {children}
    </div>
  ),
  Image: ({ alt, ...props }) => <img data-testid="roo-ui-image" alt={alt} {...props} />,
}));

let mockPointsRedemptionEnabled = true;

jest.mock('config/flags', () => ({
  get POINTS_REDEMPTION_ENABLED() {
    return mockPointsRedemptionEnabled;
  },
}));

const mockStore = configureStore([]);

const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <LoginPrompt />
    </Provider>,
  );
};

beforeEach(() => {
  jest.clearAllMocks();
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useLoginUrl.mockReturnValue({ loginUrl: '/hotels/auth/callback?state=/foo' });
  useCtaClickEvent.mockReturnValue({ ctaClickEvent: mockCtaClickEvent });
});

describe('renders the login prompt details', () => {
  it('renders the call-to-action text', () => {
    renderComponent();
    expect(screen.getByTestId('login-prompt')).toBeInTheDocument();
  });

  it('emits ctaClickEvent when the login button is clicked', async () => {
    renderComponent();
    const loginButton = screen.getByTestId('login-button');

    await userEvent.click(loginButton);

    expect(mockCtaClickEvent).toHaveBeenCalledTimes(1);
    expect(mockCtaClickEvent).toHaveBeenCalledWith({
      itemText: 'Login',
      itemType: 'button',
      url: '/hotels/auth/callback?state=/foo',
    });
  });
});

describe('LoginPrompt when POINTS_REDEMPTION_ENABLED is false', () => {
  it('does not render', () => {
    mockPointsRedemptionEnabled = false;

    renderComponent();

    expect(screen.queryByTestId('login-prompt')).not.toBeInTheDocument();
  });
});
