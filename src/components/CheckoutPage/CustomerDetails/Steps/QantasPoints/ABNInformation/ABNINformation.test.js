import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import ABNInformation from './ABNInformation';
import { useDataLayer } from 'hooks/useDataLayer';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'emotion-theming';
import theme from 'lib/theme';
import { configureStore } from 'redux-mock-store';
import { useModal } from 'lib/hooks';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');

jest.mock('lib/hooks', () => ({
  useModal: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Text: ({ 'data-testid': dataTestId, children }) => <span data-testid={dataTestId}>{children}</span>,
  Link: ({ 'data-testid': dataTestId, children, ...props }) => (
    <a data-testid={dataTestId} {...props}>
      {children}
    </a>
  ),
  Box: ({ children }) => <div data-testid="roo-ui-box">{children}</div>,
}));

jest.mock('components/Modal', () => {
  return ({ children, isOpen, onClose, title, ...props }) => {
    return isOpen ? (
      <div data-testid="mock-modal" {...props}>
        <h2 data-testid="mock-modal-title">{title}</h2>
        <button data-testid="mock-modal-close-button" onClick={onClose}>
          Close
        </button>
        {children}
      </div>
    ) : null;
  };
});

const emitInteractionEvent = jest.fn();
const mockStore = configureStore([]);

const renderComponent = () => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <ABNInformation />
      </ThemeProvider>
    </Provider>,
  );
};

describe('<ABNInformation />', () => {
  describe('Clicking the Qantas Business Rewards Link', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      useDataLayer.mockReturnValue({ emitInteractionEvent });
      useModal.mockImplementation(() => ({
        openModal: jest.fn(),
        modalProps: {
          isOpen: false,
          onClose: jest.fn(),
        },
      }));
    });

    it('should open the modal with correct text and title', async () => {
      const mockOpenModal = jest.fn();
      const mockCloseModal = jest.fn();

      useModal.mockImplementationOnce(() => ({
        openModal: mockOpenModal,
        modalProps: {
          isOpen: false,
          onClose: mockCloseModal,
        },
      }));

      const { rerender } = renderComponent();

      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();

      const learnMoreLink = screen.getByTestId('qantas-business-rewards-link');
      await userEvent.click(learnMoreLink);

      expect(mockOpenModal).toHaveBeenCalledTimes(1);

      useModal.mockImplementationOnce(() => ({
        openModal: mockOpenModal,
        modalProps: {
          isOpen: true,
          onClose: mockCloseModal,
        },
      }));

      rerender(<ABNInformation />);

      const modalTitle = screen.getByTestId('mock-modal-title');
      expect(modalTitle).toBeInTheDocument();
      expect(modalTitle).toHaveTextContent(/Earn Qantas Points for your business/i);

      expect(
        screen.getByText(
          /Add your Qantas Business Rewards membership ABN number here to earn Qantas Points for your business on top of Qantas Points for the traveller on this stay./i,
        ),
      ).toBeInTheDocument();

      expect(screen.getByTestId('privacy-policy-statement')).toBeInTheDocument();
      expect(screen.getByTestId('manage-business-travel-statement')).toBeInTheDocument();
      expect(screen.getByTestId('privacy-policy-link')).toBeInTheDocument();
      expect(screen.getByTestId('business-travel-link')).toBeInTheDocument();
    });

    it('dispatches an event to the dataLayer', async () => {
      renderComponent();
      const learnMoreLink = screen.getByTestId('qantas-business-rewards-link');
      await userEvent.click(learnMoreLink);
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Business Rewards',
        value: 'Learn Link Selected',
      });
    });

    it('should close the modal when the close button is clicked', async () => {
      const mockOpenModal = jest.fn();
      const mockCloseModal = jest.fn();

      useModal.mockImplementationOnce(() => ({
        openModal: mockOpenModal,
        modalProps: {
          isOpen: true,
          onClose: mockCloseModal,
        },
      }));

      const { rerender } = renderComponent();

      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      expect(screen.getByTestId('mock-modal-title')).toBeInTheDocument();

      const closeButton = screen.getByTestId('mock-modal-close-button');
      await userEvent.click(closeButton);

      expect(mockCloseModal).toHaveBeenCalledTimes(1);

      useModal.mockImplementationOnce(() => ({
        openModal: mockOpenModal,
        modalProps: {
          isOpen: false,
          onClose: mockCloseModal,
        },
      }));
      rerender(<ABNInformation />);

      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
      expect(screen.queryByTestId('mock-modal-title')).not.toBeInTheDocument();
    });
  });
});
