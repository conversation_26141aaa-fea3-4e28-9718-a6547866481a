import React from 'react';
import { Decimal } from 'decimal.js';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import PriceBreakdownModal from './PriceBreakdownModal';
import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
} from 'store/checkout/checkoutSelectors';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { useModal } from 'lib/hooks';
import { getQuotePriceStrikethrough } from 'components/PriceStrikethrough/helper';

jest.mock('components/PaymentBreakdown', () => {
  return function MockPaymentBreakdown(props) {
    return <div data-testid="payment-breakdown-mock">{JSON.stringify(props)}</div>;
  };
});

jest.mock('components/PriceStrikethrough/helper', () => ({
  getQuotePriceStrikethrough: jest.fn(),
}));

jest.mock('components/Modal', () => {
  return ({ children, isOpen, onClose, ...props }) =>
    isOpen ? (
      <div data-testid="mock-modal" {...props}>
        <button data-testid="mock-close-button" onClick={onClose}>
          Close
        </button>
        {children}
      </div>
    ) : null;
});

jest.mock('lib/hooks', () => ({
  useModal: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Heading: {
    h4: ({ 'data-testid': dataTestId, alt, children, ...props }) => (
      <h4 data-testid={dataTestId} alt={alt} {...props}>
        {children}
      </h4>
    ),
  },
  Flex: ({ 'data-testid': dataTestId, children, ...props }) => (
    <div data-testid={dataTestId} {...props}>
      {children}
    </div>
  ),
  Box: ({ children, ...props }) => (
    <div data-testid="roo-ui-box" {...props}>
      {children}
    </div>
  ),
  Text: jest.fn(({ 'data-testid': dataTestId, children }) => <span data-testid={dataTestId}>{children}</span>),
}));

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/useDataLayer');

const voucherAmount = new Decimal(50);
const travelPassAmount = new Decimal(100);
const pointsAmount = new Decimal(1000);
const payableLaterDueDate = new Date(2020, 10, 10);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(100);

const payableAtPropertyTotal = {
  amount: '10.00',
  currency: 'AUD',
};

const payableAtBookingBaseRate = {
  amount: '320.00',
  currency: 'AUD',
};

const property = {
  name: 'Test hotel',
};

const taxDisplayable = {
  amount: '12.00',
  currency: 'AUD',
};

const extraOccupantCharge = {
  amount: '50.00',
  currency: 'AUD',
};

const taxRecoveryBreakdown = [
  {
    description: 'TaxAndServiceFee',
    charge: {
      amount: '12.00',
      currency: 'AUD',
    },
  },
  {
    description: 'PropertyFee',
    charge: {
      amount: 20.0,
      currency: 'AUD',
    },
  },
];

const strikethrough = {
  total: {
    pointsStrikethrough: 12000,
  },
};

const offer = {
  type: 'standard',
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
    payableAtBooking: {
      baseRate: payableAtBookingBaseRate,
      taxDisplayable,
      taxRecoveryBreakdown,
      extraOccupantCharge,
      ...strikethrough,
    },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);

const defaultProps = {
  property,
  offer,
  checkIn,
  checkOut,
};

const emitInteractionEvent = jest.fn();
const mockStore = configureStore();
let store = mockStore({});

const renderComponent = (props = {}) => {
  return render(
    <Provider store={store}>
      <PriceBreakdownModal {...defaultProps} {...props} />
    </Provider>,
  );
};

describe('<PriceBreakdownModal />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getVoucherAmount.mockReturnValue(voucherAmount);
    getTravelPassAmount.mockReturnValue(travelPassAmount);
    getPointsAmount.mockReturnValue(pointsAmount);
    getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
    getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
    getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
    getQuotePriceStrikethrough.mockReturnValue(strikethrough);
    useDataLayer.mockReturnValue({ emitInteractionEvent });
    useModal.mockImplementation(() => ({
      openModal: jest.fn(),
      modalProps: {
        isOpen: false,
        onClose: jest.fn(),
      },
    }));
  });
  it('renders the modal when clicked', async () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: false,
        onClose: mockCloseModal,
      },
    }));

    const { rerender } = renderComponent();

    const modalButton = screen.getByTestId('price-breakdown-button');
    await userEvent.click(modalButton);

    expect(mockOpenModal).toHaveBeenCalledTimes(1);

    useModal.mockImplementationOnce(() => ({
      openModal: mockOpenModal,
      modalProps: {
        isOpen: true,
        onClose: mockCloseModal,
      },
    }));

    rerender(
      <Provider store={store}>
        <PriceBreakdownModal {...defaultProps} />
      </Provider>,
    );

    expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
  });

  it('dispatches an event to the data layer when clicked', async () => {
    renderComponent();
    const modalButton = screen.getByTestId('price-breakdown-button');
    await userEvent.click(modalButton);

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Summary View',
      value: 'Price Breakdown Selected',
    });
  });

  describe('when the modal is open', () => {
    const mockOpenModal = jest.fn();
    const mockCloseModal = jest.fn();
    beforeEach(() => {
      useModal.mockImplementationOnce(() => ({
        openModal: mockOpenModal,
        modalProps: {
          isOpen: true,
          onClose: mockCloseModal,
        },
      }));
    });

    it('renders the property name', () => {
      renderComponent();
      expect(screen.getByRole('heading', { name: property.name })).toBeInTheDocument();
    });

    it('renders the base rate for standard offer', () => {
      renderComponent();

      const baseRate = screen.getByTestId('stay-duration-and-base-rate');
      expect(baseRate).toHaveTextContent('1 night stay$320.00AUD');
    });

    it('renders the base rate in points for classic offers', () => {
      const classicOffer = { ...offer, type: 'classic' };
      renderComponent({ offer: classicOffer });
      const baseRate = screen.getByTestId('stay-duration-and-base-rate');
      expect(baseRate).toHaveTextContent('1 night stay1,000PTS');
    });

    it('renders the tax displayable fee', () => {
      renderComponent();
      const tax = screen.getByTestId('tax');

      expect(tax).toHaveTextContent('Tax and service fee$12.00AUD');
    });

    it('does not render the tax when taxDisplayable amount is 0', () => {
      const offerWithoutTax = {
        ...offer,
        charges: {
          ...offer.charges,
          payableAtBooking: { ...offer.charges.payableAtBooking, taxDisplayable: { amount: '0' } },
        },
      };
      renderComponent({ offer: offerWithoutTax });

      expect(screen.queryByTestId('tax')).not.toBeInTheDocument();
    });

    it('renders the extra occupant charge', () => {
      renderComponent();

      const extraOccupant = screen.getByTestId('extra-occupant');
      expect(extraOccupant).toHaveTextContent('Extra occupant charge$50.00AUD');
    });

    it('does not render the extra occupant charge when amount is 0', () => {
      const offerWithoutOccupantCharge = {
        ...offer,
        charges: {
          ...offer.charges,
          payableAtBooking: { ...offer.charges.payableAtBooking, extraOccupantCharge: { amount: '0' } },
        },
      };
      renderComponent({ offer: offerWithoutOccupantCharge });
      expect(screen.queryByTestId('extra-occupant')).not.toBeInTheDocument();
    });

    it('renders the property fee', () => {
      renderComponent();
      const propertyFee = screen.getByTestId('property-fee');
      expect(propertyFee).toHaveTextContent('Property fee$20.00AUD');
    });

    it('does not render the property fee when amount is 0', () => {
      const offerWithoutPropertyFee = {
        ...offer,
        charges: {
          ...offer.charges,
          payableAtBooking: {
            ...offer.charges.payableAtBooking,
            taxRecoveryBreakdown: [
              {
                description: 'TaxAndServiceFee',
                charge: {
                  amount: '12.00',
                  currency: 'AUD',
                },
              },
              {
                description: 'PropertyFee',
                charge: {
                  amount: '0',
                  currency: 'AUD',
                },
              },
            ],
          },
        },
      };

      renderComponent({ offer: offerWithoutPropertyFee });
      renderComponent();

      expect(screen.queryByTestId('property-fee')).not.toBeInTheDocument();
    });

    it('renders the voucher amount', () => {
      renderComponent();
      const voucher = screen.getByTestId('voucher');
      expect(voucher).toHaveTextContent('Voucher$-50.00AUD');
    });

    it('does not render the voucher when amount is 0', () => {
      getVoucherAmount.mockReturnValue(new Decimal(0));
      renderComponent();
      expect(screen.queryByTestId('voucher')).not.toBeInTheDocument();
    });

    it('renders the PaymentBreakdown component with correct props', async () => {
      renderComponent();

      const paymentBreakdownMock = screen.getByTestId('payment-breakdown-mock');
      expect(paymentBreakdownMock).toBeInTheDocument();
      expect(paymentBreakdownMock).toHaveTextContent(
        JSON.stringify({
          payableNowCashAmount,
          payableLaterCashAmount,
          payableLaterDueDate,
          pointsAmount,
          travelPassAmount,
          payableAtProperty: payableAtPropertyTotal,
          priceStrikethrough: strikethrough,
        }),
      );
    });

    it('closes the modal when the close button is clicked', async () => {
      const { rerender } = renderComponent();
      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      await userEvent.click(screen.getByTestId('mock-close-button'));

      expect(mockCloseModal).toHaveBeenCalledTimes(1);

      useModal.mockImplementationOnce(() => ({
        openModal: jest.fn(),
        modalProps: {
          isOpen: false,
          onClose: jest.fn(),
        },
      }));

      rerender(
        <Provider store={store}>
          <PriceBreakdownModal {...defaultProps} />
        </Provider>,
      );

      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
    });
  });
});
