import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageLoader from './ImageLoader';

jest.useFakeTimers();
jest.mock('@qga/roo-ui/components', () => ({
  Box: ({ children, ...props }) => <div {...props}>{children}</div>,
}));

const BAD_IMAGE_URL = 'BAD IMAGE URL';
const SLOW_IMAGE_URL = 'SLOW IMAGE URL';
const REALLY_SLOW_IMAGE_URL = 'REALLY SLOW IMAGE URL';
const GOOD_IMAGE_URL = 'GOOD IMAGE URL';

beforeAll(() => {
  jest.spyOn(global.Image.prototype, 'naturalWidth', 'get').mockReturnValue('101px');
  jest.spyOn(global.Image.prototype, 'naturalHeight', 'get').mockReturnValue('202px');
  jest.spyOn(global.Image.prototype, 'src', 'get').mockImplementation(function () {
    return this._src;
  });
  jest.spyOn(global.Image.prototype, 'src', 'set').mockImplementation(function (src) {
    this._src = src;
    switch (src) {
      case BAD_IMAGE_URL:
        this.onerror();
        break;
      case SLOW_IMAGE_URL:
        setTimeout(() => this.onload(), 400);
        break;
      case REALLY_SLOW_IMAGE_URL:
        setTimeout(() => this.onload(), 3000);
        break;
      default:
        this.onload();
        break;
    }
  });
});

afterAll(() => jest.restoreAllMocks());

const alt = 'Some test image';
const srcSet = 'some/image.jpg 1x, some/image-x2.jpg x2';
const defaultProps = { srcSet, alt };

describe('ImageLoader', () => {
  it('renders a fallback image when there is an error', async () => {
    render(<ImageLoader {...defaultProps} src={BAD_IMAGE_URL} />);
    await waitFor(() => {
      expect(screen.getByRole('img')).toBeInTheDocument();
    });

    expect(screen.getByRole('img')).toHaveAttribute('src', 'placeholder.svg');
    expect(screen.getByRole('img')).toHaveAttribute('alt', alt);
  });

  it('renders a placeholder when the image is slightly slow', async () => {
    render(<ImageLoader {...defaultProps} src={SLOW_IMAGE_URL} alt={alt} />);
    expect(screen.getByTestId('placeholder')).toBeInTheDocument();

    act(() => jest.advanceTimersByTime(500));

    await waitFor(() => {
      expect(screen.getByRole('img')).toBeInTheDocument();
    });
    expect(screen.getByRole('img')).toHaveAttribute('src', SLOW_IMAGE_URL);
    expect(screen.getByRole('img')).toHaveAttribute('srcset', srcSet);
    expect(screen.queryByTestId('placeholder')).not.toBeInTheDocument();
  });

  it('renders a loader when the image is slow', async () => {
    render(<ImageLoader {...defaultProps} src={REALLY_SLOW_IMAGE_URL} />);
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();

    act(() => jest.advanceTimersByTime(500));
    expect(screen.getByRole('progressbar')).toBeInTheDocument();

    jest.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
    });
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('src', REALLY_SLOW_IMAGE_URL);
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('srcset', srcSet);
    expect(screen.queryByText('ImageLoadingIndicator')).not.toBeInTheDocument();
  });

  it('renders an image with the correct attributes', async () => {
    render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} />);
    await waitFor(() => {
      expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
    });
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('src', GOOD_IMAGE_URL);
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('srcset', srcSet);
  });

  it('uses provided width and height', async () => {
    render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} width="88px" height="99px" />);
    await waitFor(() => {
      expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
    });

    screen.debug();

    expect(screen.getByTestId('mock-box')).toHaveAttribute('weight', '88px');
    expect(screen.getByTestId('mock-box')).toHaveAttribute('height', '99px');
  });

  it('uses 100% for missing dimensions when one dimension is supplied', async () => {
    render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} width="88px" />);
    await waitFor(() => {
      expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
    });
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('width', '88px');
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('height', '100%');
  });

  it('uses image dimensions when width and height are not provided', async () => {
    render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} />);
    await waitFor(() => {
      expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
    });
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('width', '101px');
    expect(screen.getByRole('img', { name: alt })).toHaveAttribute('height', '202px');
  });

  it('renders a hidden image initially to allow preloading', () => {
    render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} />);
    // The image itself is not in the document yet, but the underlying img element that loads it is
    const hiddenImage = screen.getByTestId('hidden-image-preload');
    expect(hiddenImage).toBeInTheDocument();
    expect(hiddenImage).toHaveAttribute('src', GOOD_IMAGE_URL);
    expect(hiddenImage).toHaveAttribute('srcset', srcSet);
    expect(hiddenImage).toHaveStyle('position: absolute; left: -9999px;');
  });

  //   it('does not render placeholder or loader if image loads instantly', async () => {
  //     render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} />);
  //     expect(screen.queryByTestId('placeholder')).not.toBeInTheDocument();
  //     expect(screen.queryByText('ImageLoadingIndicator')).not.toBeInTheDocument();
  //     await waitFor(() => {
  //       expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
  //     });
  //   });

  //   it('applies custom className to the rendered image', async () => {
  //     render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} className="custom-image-class" />);
  //     await waitFor(() => {
  //       expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
  //     });
  //     expect(screen.getByRole('img', { name: alt })).toHaveClass('custom-image-class');
  //   });

  //   it('applies custom style to the rendered image', async () => {
  //     render(<ImageLoader {...defaultProps} src={GOOD_IMAGE_URL} style={{ border: '1px solid red' }} />);
  //     await waitFor(() => {
  //       expect(screen.getByRole('img', { name: alt })).toBeInTheDocument();
  //     });
  //     expect(screen.getByRole('img', { name: alt })).toHaveStyle('border: 1px solid red;');
  //   });
});
